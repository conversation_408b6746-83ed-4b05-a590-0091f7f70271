package com.unipus.digitalbook.aop.log;

import ch.qos.logback.classic.spi.ILoggingEvent;
import ch.qos.logback.classic.spi.IThrowableProxy;
import ch.qos.logback.core.AppenderBase;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.MediaType;
import org.springframework.web.reactive.function.client.WebClient;

import java.util.Arrays;

@Configuration
@Slf4j
public class FeishuLogAppender extends AppenderBase<ILoggingEvent> {

    private static final String webhookUrl = "https://open.feishu.cn/open-apis/bot/v2/hook/4a52098a-26e5-4f05-9ad3-331d75b1173c";
    private final WebClient webClient = WebClient.create();

    @Override
    protected void append(ILoggingEvent eventObject) {
        if (!eventObject.getLevel().isGreaterOrEqual(ch.qos.logback.classic.Level.ERROR)) {
            return;
        }
        String body = FeishuCard.build("【异常日志】", eventObject.getFormattedMessage(), getStackTrace(eventObject));
        System.out.println(body);

        webClient.post()
            .uri(webhookUrl)
            .contentType(MediaType.APPLICATION_JSON)
            .bodyValue(body)
            .retrieve()
            .bodyToMono(String.class)
            .subscribe(resp -> log.info("飞书响应: {}", resp),
                    err -> log.error("飞书推送失败", err));
    }

    private String getStackTrace(ILoggingEvent event) {
        IThrowableProxy tp = event.getThrowableProxy();
        if ( tp==null) return "";

        StringBuilder sb = new StringBuilder();
        sb.append(tp.getClassName()).append(": ").append(tp.getMessage()).append("\n");

        Object[] array = Arrays.stream(tp.getStackTraceElementProxyArray()).toArray();
        Arrays.stream(array).forEach(p->sb.append("\tat ").append(p).append("\n"));

        return sb.toString();
    }


    public static class FeishuCard {
        private static final String template =
                """
                    {
                      "name": "$title$",
                      "dsl": {
                        "schema": "2.0",
                        "config": {
                          "update_multi": true,
                          "style": {
                            "text_size": {
                              "normal_v2": {
                                "default": "normal",
                                "pc": "normal",
                                "mobile": "heading"
                              }
                            }
                          }
                        },
                        "body": {
                          "direction": "vertical",
                          "padding": "12px 12px 12px 12px",
                          "elements": [
                            {
                              "tag": "markdown",
                              "content": "$detail$",
                              "text_align": "left",
                              "text_size": "normal_v2",
                              "margin": "0px 0px 0px 0px"
                            }
                          ]
                        },
                        "header": {
                          "title": {
                            "tag": "plain_text",
                            "content": "$title$"
                          },
                          "subtitle": {
                            "tag": "plain_text",
                            "content": "$message$"
                          },
                          "template": "orange",
                          "padding": "12px 12px 12px 12px"
                        }
                      },
                      "variables": []
                    }
                """;

        static public String build(String title, String message, String detail) {
            String cardStr = template.replace("$title$", title);
            cardStr = cardStr.replace("$message$", message);
            cardStr = cardStr.replace("$detail$", detail);
            return cardStr; // Removed redundant semicolon
        }
    }
}